@echo off
chcp 65001 >nul
echo 正在以管理员身份运行AutoHotkey脚本...
echo.

REM 检查AutoHotkey是否已安装
where autohotkey >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到AutoHotkey v2
    echo 请确保已安装AutoHotkey v2并添加到系统PATH中
    echo.
    echo 您可以从以下地址下载：https://www.autohotkey.com/
    echo.
    pause
    exit /b 1
)

REM 检查脚本文件是否存在
if not exist "%~dp0mouse_side_key_toggle.ahk" (
    echo 错误：未找到脚本文件 mouse_side_key_toggle.ahk
    echo 请确保脚本文件与此批处理文件在同一目录中
    echo.
    pause
    exit /b 1
)

echo 启动脚本中...
echo.

REM 以管理员身份运行脚本
powershell -Command "Start-Process 'autohotkey' -ArgumentList '%~dp0mouse_side_key_toggle.ahk' -Verb RunAs"

if %errorlevel% equ 0 (
    echo 脚本已启动成功！
    echo.
    echo 使用说明：
    echo - 长按鼠标侧键1（XButton1）可启动/停止自动输入
    echo - 启动后每2秒会输入一次"测试输入"
    echo - 可通过系统托盘图标查看状态
    echo - 右键托盘图标可退出程序
    echo.
) else (
    echo 启动失败，可能是用户取消了管理员权限请求
    echo.
)

echo 按任意键退出此窗口...
pause >nul
