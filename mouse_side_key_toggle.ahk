#Requires AutoHotkey v2.0
; 日期: 2025-08-01
; 功能: 鼠标侧键1长按启动/停止自动输入"测试输入"

; 检查管理员权限并自动提升
if !A_IsAdmin {
    try {
        ; 以管理员身份重新运行脚本
        Run('*RunAs "' . A_ScriptFullPath . '"')
    }
    ExitApp
}

; 全局变量
isRunning := false
inputTimer := 0

; 创建托盘菜单
A_TrayMenu.Delete()
A_TrayMenu.Add("状态: 已停止", MenuHandler)
A_TrayMenu.Add()
A_TrayMenu.Add("退出", (*) => ExitApp())
A_TrayMenu.Default := "状态: 已停止"

; 更新托盘状态显示
UpdateTrayStatus() {
    global isRunning
    A_TrayMenu.Delete("状态: 已停止")
    A_TrayMenu.Delete("状态: 运行中")
    
    if (isRunning) {
        A_TrayMenu.Insert("1&", "状态: 运行中", MenuHandler)
        A_TrayMenu.Default := "状态: 运行中"
        A_IconTip := "自动输入 - 运行中"
    } else {
        A_TrayMenu.Insert("1&", "状态: 已停止", MenuHandler)
        A_TrayMenu.Default := "状态: 已停止"
        A_IconTip := "自动输入 - 已停止"
    }
}

; 菜单处理函数
MenuHandler(*) {
    ; 点击状态项时切换状态
    ToggleInput()
}

; 切换输入状态
ToggleInput() {
    global isRunning, inputTimer
    
    isRunning := !isRunning
    
    if (isRunning) {
        ; 启动自动输入
        StartAutoInput()
        ToolTip("自动输入已启动", , , 1)
        SetTimer(() => ToolTip("", , , 1), 2000)
    } else {
        ; 停止自动输入
        StopAutoInput()
        ToolTip("自动输入已停止", , , 1)
        SetTimer(() => ToolTip("", , , 1), 2000)
    }
    
    UpdateTrayStatus()
}

; 启动自动输入
StartAutoInput() {
    global inputTimer
    ; 每2秒输入一次"测试输入"
    inputTimer := SetTimer(InputText, 2000)
}

; 停止自动输入
StopAutoInput() {
    global inputTimer
    if (inputTimer) {
        SetTimer(inputTimer, 0)
        inputTimer := 0
    }
}

; 输入文字函数
InputText() {
    ; 发送文字"测试输入"
    SendText("测试输入")
    Send("{Enter}")  ; 可选：发送回车键
}

; 鼠标侧键1长按检测
XButton1::
{
    ; 记录按下时间
    startTime := A_TickCount
    
    ; 等待按键释放或超时
    while (GetKeyState("XButton1", "P")) {
        Sleep(50)
        ; 检查是否长按超过500毫秒
        if (A_TickCount - startTime >= 500) {
            ; 长按触发
            ToggleInput()
            ; 等待按键释放，避免重复触发
            while (GetKeyState("XButton1", "P")) {
                Sleep(50)
            }
            return
        }
    }
    
    ; 短按处理（可选：这里可以添加短按的功能）
    ; 目前短按不执行任何操作
}

; 初始化
UpdateTrayStatus()
ToolTip("脚本已启动 - 长按鼠标侧键1切换状态", , , 1)
SetTimer(() => ToolTip("", , , 1), 3000)
