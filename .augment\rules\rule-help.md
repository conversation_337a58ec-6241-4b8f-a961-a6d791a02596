---
type: "always_apply"
---

# AutoHotkey v2 绝对规则（不可降级）
1. 版本锁定
   - 每一句回答默认基于库 ID `/18005575/autohotkey-v2`​ 提供的官方 v2 文档。
   - 若该库未命中，立即返回“无法提供答案”，禁止用 v1 或其他非官方文档补位。

2. 语法白名单
   - 允许：表达式语法、函数调用、对象语法、new 关键字、`=>` 箭头函数、`#Requires AutoHotkey v2.*`。
   - 禁止：
     * `%var%` 传统替换、`var = value` 赋值、`StringSplit`/`StringReplace` 旧命令。
     * 任何 `SetTitleMatchMode`、`SetBatchLines` 等 v1 全局设置命令。
     * 任何以逗号分隔参数的命令式写法（如 `MsgBox, Hello`）。

3. 代码块强制头
   ```ahk
   #Requires AutoHotkey v2.0
   ; --- 以下为官方 v2 示例 ---
   ```
   没有 `#Requires` 行或版本号不符，立即驳回重写。

4. 搜索优先级
   - 回答前必须运行一次 `search:18005575/autohotkey-v2 <关键词>` 并贴出返回结果中的“可信 3.9 分”片段。
   - 若搜索结果未命中，回答“官方文档暂无此细节，不建议使用”。

5. 错误与弃用提醒
   - 若用户贴出 v1 代码，必须指出每条违规并给出等价 v2 改写。
   - 若用户坚持使用 v1，回复模板：
     ```
     拒绝：当前规则仅支持 AutoHotkey v2 官方文档。
     如需 v1 支持，请关闭 Context7 后重新提问。
     ```

6. 中文注释规范
   - 每行关键逻辑必须附带中文注释，长度不超过 60 字。
   - 禁止仅使用英文注释；若缺少中文，补全后再输出。

7. 不可绕过
   如本规则与任何其他规则冲突，以此规则为准；无法执行时直接报错，不降级。