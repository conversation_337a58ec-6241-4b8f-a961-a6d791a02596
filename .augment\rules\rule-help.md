---
type: "always_apply"
---

# AutoHotkey v2 绝对规则（不可降级）

1. **版本锁定**
   - 所有回答默认基于库 ID `/18005575/autohotkey-v2` 提供的官方 v2 文档。
   - 若该库未命中，立即返回「无法提供答案」，禁止用 v1 或其他非官方文档补位。

2. **语法白名单**
   - **允许**：表达式语法、函数调用、对象语法、`=>` 箭头函数、`#Requires AutoHotkey v2.*`。
   - **禁止**：
     * `%var%` 传统替换、`var = value` 赋值、`StringSplit`/`StringReplace` 等旧命令；
     * 任何 `SetTitleMatchMode`、`SetBatchLines` 等 v1 全局设置命令；
     * 任何逗号分隔参数的命令式写法（如 `MsgBox, Hello`）。

3. **代码块强制头模板**
   在给出 **完整可运行脚本** 时，必须在代码块首行使用下列模板，且仅出现一次：

   ```ahk
   #Requires AutoHotkey v2.0
   ; 日期: <YYYY-MM-DD>
   ; 功能: <一句话描述脚本用途>
   ```

   - 若缺失 `#Requires AutoHotkey v2.0` 或版本号不符，立即驳回重写。
   - 其余两行元信息可留空，但不可删除注释标记 `;`。
   - 若仅提供零散片段，可省略此行，但必须显式提示“需自行补充 `#Requires AutoHotkey v2.0`”。

4. **搜索优先级**
   - 回答前必须执行 `search:18005575/autohotkey-v2 <关键词>`，并贴出返回结果中“可信 3.9 分”片段。
   - 若搜索结果未命中，回答「官方文档暂无此细节，不建议使用」。

5. **错误与弃用提醒**
   - 若用户贴出 v1 代码，逐条指出违规并给出等价 v2 改写。
   - 若用户坚持使用 v1，回复模板：
     ```
     拒绝：当前规则仅支持 AutoHotkey v2 官方文档。
     如需 v1 支持，请关闭 Context7 后重新提问。
     ```

6. **中文注释**  
   - 核心逻辑用 **简洁中文** 注释即可，**不必逐行**；  
   - 一行注释尽量 **不超过 40 字**，避免刷屏；  
   - 英文专有名词可保留，不必硬翻；  
   - 示例：  
     ```ahk
     #Requires AutoHotkey v2.0
     ; 一键最大化当前窗口
     ^!m::WinMaximize "A"

7. **不可绕过**
   如本规则与其他规则冲突，以此为准；无法执行时直接报错，不降级。